import { Form, Formik } from 'formik';
import React, { useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import { FaMagnifyingGlass } from 'react-icons/fa6';
import { useNavigate } from 'react-router-dom';
import BackButton from '../../../Components/BackButton';
import ChartOfAccountForm from '../../../Components/ChartOfAccountForm/ChartOfAccountForm';
import CustomButton from '../../../Components/CustomButton';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import PartyLedgerForm from '../../../Components/PartyLedgerForm/PartyLedgerForm';
import WalkInCustomerForm from '../../../Components/WalkInCustomerForm/WalkInCustomerForm';
import { usePageTitle } from '../../../Hooks/usePageTitle';
import { getCurrencyOptions } from '../../../Utils/Utils';
import '../transactionStyles.css';
import CurrencyTransferTable from './CurrencyTransferTable';
import EditCurrencyTransfer from './EditCurrencyTransfer';
import NewCurrencyTransfer from './NewCurrencyTransfer';
import ViewCurrencyTransfer from './ViewCurrencyTransfer';
// import EditSuspenseVoucher from './EditSuspenseVoucher';

const CurrencyTransfer = () => {
  usePageTitle('Currency Transfer');
  const currencyOptions = getCurrencyOptions();
  const navigate = useNavigate();

  // [new, view, edit,  listing]
  // View is for specific Suspense Voucher search and view it's detail
  // Edit is for editing the specific Suspense Voucher's detail
  // Listing is for Suspense Voucher listing table
  const [pageState, setPageState] = useState('new');

  const [isDisabled, setIsDisabled] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [cloneSearchTerm, setCloneSearchTerm] = useState('');
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [showAddLedgerModal, setShowAddLedgerModal] = useState('');
  const [newlyCreatedAccount, setNewlyCreatedAccount] = useState(null);
  const [showAddOfficeLocationModal, setShowAddOfficeLocationModal] =
    useState(false);
  // Upload Only Modal
  const [uploadAttachmentsModal, setUploadAttachmentsModal] = useState(false);
  // Upload And View Modal
  const [attachmentsModal, setAttachmentsModal] = useState(false);
  // Selected files from UploadAttachments Modal
  const [selectedFiles, setSelectedFiles] = useState(null);
  const [showMissingCurrencyRateModal, setShowMissingCurrencyRateModal] =
    useState(false);
  const [currencyToSelect, setCurrencyToSelect] = useState(null);

  const renderAddLedgerForm = () => {
    switch (showAddLedgerModal) {
      case 'add new pl':
        return (
          <PartyLedgerForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new wic':
        return (
          <WalkInCustomerForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      case 'add new gl':
        return (
          <ChartOfAccountForm
            inPopup
            onSuccess={(newlyCreatedAccount) => {
              console.log(newlyCreatedAccount);
              setNewlyCreatedAccount(newlyCreatedAccount);
              setShowAddLedgerModal('');
            }}
            onCancel={() => setShowAddLedgerModal('')}
          />
        );
      default:
        break;
    }
  };

  const renderPageContent = () => {
    const pageComponents = {
      new: (
        <NewCurrencyTransfer
          isDisabled={isDisabled}
          setIsDisabled={setIsDisabled}
          setShowAddOfficeLocationModal={setShowAddOfficeLocationModal}
          setShowAddLedgerModal={setShowAddLedgerModal}
          newlyCreatedAccount={newlyCreatedAccount}
          uploadAttachmentsModal={uploadAttachmentsModal}
          setUploadAttachmentsModal={setUploadAttachmentsModal}
          selectedFiles={selectedFiles}
          setSelectedFiles={setSelectedFiles}
          setShowMissingCurrencyRateModal={setShowMissingCurrencyRateModal}
          setCurrencyToSelect={setCurrencyToSelect}
          currencyOptions={currencyOptions} // Following JournalVoucherRow pattern
          date={date} // Following JournalVoucherRow pattern
        />
      ),
      view: (
        <ViewCurrencyTransfer
          setPageState={setPageState}
          attachmentsModal={attachmentsModal}
          setAttachmentsModal={setAttachmentsModal}
        />
      ),
      listing: <CurrencyTransferTable setPageState={setPageState} />,
      edit: (
        <EditCurrencyTransfer
          setPageState={setPageState}
          setShowAddOfficeLocationModal={setShowAddOfficeLocationModal}
          setShowAddLedgerModal={setShowAddLedgerModal}
          newlyCreatedAccount={newlyCreatedAccount}
          uploadAttachmentsModal={uploadAttachmentsModal}
          setUploadAttachmentsModal={setUploadAttachmentsModal}
          selectedFiles={selectedFiles}
          setSelectedFiles={setSelectedFiles}
          setShowMissingCurrencyRateModal={setShowMissingCurrencyRateModal}
          setCurrencyToSelect={setCurrencyToSelect}
          currencyOptions={currencyOptions} // Following JournalVoucherRow pattern
          date={date} // Following JournalVoucherRow pattern
        />
      ),
    };

    return pageComponents[pageState] || null;
  };

  return (
    <>
      <section className="position-relative">
        <div
          style={{ height: 43 }}
          className="d-flex gap-3 justify-content-between align-items-center flex-wrap mb-4"
        >
          <div>
            {pageState == 'listing' && (
              <BackButton
                handleBack={() => {
                  setPageState('new');
                }}
              />
            )}
            <h2 className="screen-title mb-0">Currency Transfer</h2>
          </div>
          {pageState == 'new' && isDisabled && (
            <div className="d-flex gap-2">
              <CustomButton text={'New'} onClick={() => setIsDisabled(false)} />
            </div>
          )}
        </div>
        <Row>
          <Col xs={12}>
            <div className="d-flex align-items-start justify-content-between flex-wrap-reverse mb-3">
              <div className="d-flex align-items-end gap-2 flex-wrap mt-3">
                <CustomInput
                  style={{ width: '180px' }}
                  type="text"
                  placeholder="Search CBS"
                  error={false}
                  showBorders={false}
                  borderRadius={10}
                  name="search"
                  rightIcon={FaMagnifyingGlass}
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                  }}
                  onButtonClick={() => {
                    if (searchTerm === '') {
                      setPageState('listing');
                    } else {
                      setPageState('view');
                    }
                    console.log('search for:', searchTerm);
                  }}
                />
                <CustomInput
                  style={{ width: '250px' }}
                  type="text"
                  placeholder="Clone Currency Transfer"
                  error={false}
                  showBorders={false}
                  borderRadius={10}
                  name="cloneSearch"
                  rightText={'Clone'}
                  value={cloneSearchTerm}
                  onChange={(e) => {
                    setCloneSearchTerm(e.target.value);
                  }}
                />
              </div>
              <div>
                <CustomInput
                  name="Date"
                  label={'Date'}
                  type="date"
                  showBorders={false}
                  error={false}
                  borderRadius={10}
                  value={date}
                  onChange={(e) => {
                    setDate(e.target.value);
                  }}
                />
              </div>
            </div>
            {renderPageContent()}
          </Col>
        </Row>
      </section>

      {/* Add New Ledger Modal */}
      <CustomModal
        show={!!showAddLedgerModal}
        close={() => setShowAddLedgerModal('')}
        size="xl"
        style={{ minHeight: '812px' }}
      >
        {renderAddLedgerForm()}
      </CustomModal>

      {/* Add Office Location Modal  */}
      <CustomModal
        show={showAddOfficeLocationModal}
        close={() => setShowAddOfficeLocationModal(false)}
      >
        <div className="text-center mb-3">
          <h4 className="modalTitle">New Office Location</h4>
        </div>
        <div className="px-sm-5">
          <Formik
            initialValues={{ office_location: '' }}
            // validationSchema={addOfficeLocationValidationSchema}
            // onSubmit={handleAddOfficeLocation}
            onSubmit={() => {
              setShowAddOfficeLocationModal(false);
            }}
          >
            {({ values, errors, touched, handleChange, handleBlur }) => (
              <Form>
                <div className="mb-45">
                  <CustomInput
                    name={'office_location'}
                    type={'text'}
                    required
                    label={'Office Location'}
                    placeholder={'Enter Office Location'}
                    value={values.office_location}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.office_location && errors.office_location}
                  />
                </div>
                <div className="d-flex gap-3 justify-content-center mb-3">
                  {/* {!addOfficeLocationMutation.isPending ? ( */}
                  <>
                    <CustomButton type="submit" text={'Save'} />
                    <CustomButton
                      variant={'secondaryButton'}
                      text={'Cancel'}
                      type={'button'}
                      onClick={() => setShowAddOfficeLocationModal(false)}
                    />
                  </>
                  {/* ) : (
                    <PulseLoader size={11} className="modalLoader" />
                  )} */}
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </CustomModal>
      <CustomModal
        show={showMissingCurrencyRateModal}
        close={() => setShowMissingCurrencyRateModal(false)}
        title={'Missing Rate of Exchange'}
        description={'Rate of exchange is missing for selected currency.'}
        variant={'error'}
        btn1Text={'Update Rate of Exchange'}
        action={() => {
          console.log('Goto rate update screen');
          navigate('/transactions/remittance-rate-of-exchange', {
            state: { currencyToSelect },
          });
        }}
      />
    </>
  );
};

export default CurrencyTransfer;
