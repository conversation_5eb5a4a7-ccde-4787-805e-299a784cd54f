import { Form, Formik } from 'formik';
import React, { useCallback, useRef, useState } from 'react';
import AccountBalanceCard from '../../../Components/AccountBalanceCard/AccountBalanceCard';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import { currencyTransferNewHeaders } from '../../../Utils/Constants/TableHeaders';
import CurrencyTransferRow from './CurrencyTransferRow';
import {
  MOCK_EXCHANGE_RATES,
} from '../../../Mocks/MockData';
import { currencyTransferValidationSchema } from '../../../Utils/Validations/ValidationSchemas';
import { useNavigate } from 'react-router-dom';
import CustomButton from '../../../Components/CustomButton';
import CombinedInputs from '../../../Components/CombinedInputs/CombinedInputs';
import useAccountsByType from '../../../Hooks/useAccountsByType';
import { useQuery } from '@tanstack/react-query';
import {
  getBanks,
  getAccountBalance,
} from '../../../Services/Transaction/AccountToAccount';
import {
  getBanks as getGeneralBanks,
  getDocTypes,
  getCities,
} from '../../../Services/General';
import { useEffect } from 'react';
const generateInitialRows = (count, currencyOptions = []) => {
  const rows = {};
  // Find TMN currency option, fallback to 'DHS' if TMN not found
  const tmnCurrency = currencyOptions.find((x) => x.label === 'TMN');
  const defaultCurrency = tmnCurrency ? tmnCurrency.value : 'DHS';
  const defaultCurrencyId = tmnCurrency ? tmnCurrency.value : null;

  Array.from({ length: count }).forEach(() => {
    const id = crypto.randomUUID();
    rows[id] = {
      id,
      currency: defaultCurrency,
      currency_id: defaultCurrencyId, // Following JournalVoucherRow pattern
      rate: '', // Following JournalVoucherRow pattern
      amount: '',
      lc_amount: '', // Following JournalVoucherRow pattern
      narration: '',
      docType: '',
      docNo: '',
      bank: '',
      city: '',
      code: '',
      error: false, // Following JournalVoucherRow pattern
    };
  });
  return rows;
};
// INITIAL_STATE will be set dynamically when currencyOptions are available

const NewCurrencyTransfer = ({
  isDisabled = false,
  setIsDisabled,
  setShowAddOfficeLocationModal,
  setShowAddLedgerModal,
  newlyCreatedAccount,
  uploadAttachmentsModal,
  setUploadAttachmentsModal,
  selectedFiles,
  setSelectedFiles,
  setShowMissingCurrencyRateModal,
  setCurrencyToSelect,
  currencyOptions = [], // Following JournalVoucherRow pattern
  date, // Following JournalVoucherRow pattern
}) => {
  const navigate = useNavigate();
  const [rows, setRows] = useState({});
  const [totalDebit, setTotalDebit] = useState(0);
  const [totalCredit, setTotalCredit] = useState(0);
  const [currencyTotals, setCurrencyTotals] = useState([]); // Track totals by currency
  const formikRef = useRef();

  // Account selection state (copied from Account to Account)
  const [selectedDebitAccount, setSelectedDebitAccount] = useState(null);
  const [selectedCreditAccount, setSelectedCreditAccount] = useState(null);
  const [newAccountTriggeredFrom, setNewAccountTriggeredFrom] = useState('');

  const [showBalances, setShowBalances] = useState(false);

  // Bank account related state (copied from Account to Account)
  const [bankAccounts, setBankAccounts] = useState([]);
  const [showChequeNumber, setShowChequeNumber] = useState(false);
  const [selectedBank, setSelectedBank] = useState('');
  const [chequeOptions, setChequeOptions] = useState([]);

  // Get account options using custom hook (copied from Account to Account)
  const { getAccountsByTypeOptions } = useAccountsByType({
    includeBeneficiary: false,
    staleTime: 1000 * 60 * 5,
  });

  // Fetch bank accounts to determine which accounts are banks (copied from Account to Account)
  const { data: bankAccountsData } = useQuery({
    queryKey: ['bankAccounts'],
    queryFn: () => getBanks('bank'),
    staleTime: 1000 * 60 * 5, // 5 minutes cache
  });

  // Update bank accounts list when data is fetched (copied from Account to Account)
  useEffect(() => {
    if (bankAccountsData) {
      setBankAccounts(bankAccountsData);
    }
  }, [bankAccountsData]);

  // Initialize rows with TMN as default currency when currencyOptions are available
  useEffect(() => {
    // currencyOptions[0].value to check if currency options is not loading or doesn't have error
    if (
      currencyOptions.length > 0 &&
      Object.keys(rows).length === 0 &&
      currencyOptions[0].value
    ) {
      setRows(generateInitialRows(3, currencyOptions));
    }
  }, [currencyOptions, rows]);

  // Helper function to check if an account is a bank account (copied from Account to Account)
  const isBankAccount = (accountId) => {
    return bankAccounts.some((bank) => bank.id === accountId);
  };

  // Logic to show/hide cheque number field and set selected bank (copied from Account to Account)
  useEffect(() => {
    let shouldShowCheque = false;
    let bankId = null;

    // Check if debit account is a bank
    if (
      selectedDebitAccount?.value &&
      isBankAccount(selectedDebitAccount.value)
    ) {
      shouldShowCheque = true;
      bankId = selectedDebitAccount.value;
    }
    // Check if credit account is a bank
    else if (
      selectedCreditAccount?.value &&
      isBankAccount(selectedCreditAccount.value)
    ) {
      shouldShowCheque = true;
      bankId = selectedCreditAccount.value;
    }

    setShowChequeNumber(shouldShowCheque);

    if (bankId && bankId !== selectedBank) {
      setSelectedBank(bankId);
    } else if (!shouldShowCheque) {
      setSelectedBank('');
      setChequeOptions([]);
    }
  }, [selectedDebitAccount, selectedCreditAccount, bankAccounts]);

  // Fetch account balances (copied from Account to Account)
  const { data: debitAccountBalance } = useQuery({
    queryKey: ['accountBalance', selectedDebitAccount?.value],
    queryFn: () =>
      getAccountBalance(
        selectedDebitAccount.value,
        selectedDebitAccount.accountType
      ),
    enabled: !!selectedDebitAccount?.value,
    staleTime: 1000 * 60 * 2,
    retry: 1,
  });
  const { data: creditAccountBalance } = useQuery({
    queryKey: ['accountBalance', selectedCreditAccount?.value],
    queryFn: () =>
      getAccountBalance(
        selectedCreditAccount.value,
        selectedCreditAccount.accountType
      ),
    enabled: !!selectedCreditAccount?.value,
    staleTime: 1000 * 60 * 2,
  });

  // Fetch Banks (following General.js pattern)
  const {
    data: banks,
    isLoading: isLoadingBanks,
    isError: isErrorBanks,
    error: errorBanks,
  } = useQuery({
    queryKey: ['banks'],
    queryFn: getGeneralBanks,
    staleTime: 1000 * 60 * 5, // 5 minutes cache
  });

  // Fetch Document Types (following General.js pattern)
  const {
    data: docTypes,
    isLoading: isLoadingDocTypes,
    isError: isErrorDocTypes,
    error: errorDocTypes,
  } = useQuery({
    queryKey: ['doc-types'],
    queryFn: getDocTypes,
    staleTime: 1000 * 60 * 5, // 5 minutes cache
  });

  // Fetch Cities (following General.js pattern)
  const {
    data: cities,
    isLoading: isLoadingCities,
    isError: isErrorCities,
    error: errorCities,
  } = useQuery({
    queryKey: ['cities'],
    queryFn: getCities,
    staleTime: 1000 * 60 * 5, // 5 minutes cache
  });

  // Helper functions to transform data into options
  const getBankOptions = () => {
    if (isLoadingBanks) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }
    if (isErrorBanks) {
      console.error('Unable to fetch banks', errorBanks);
      return [{ label: 'Unable to fetch banks', value: null }];
    }
    return (
      banks?.map((x) => ({
        value: x?.id,
        label: x?.account_name,
      })) || []
    );
  };

  const getDocTypeOptions = () => {
    if (isLoadingDocTypes) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }
    if (isErrorDocTypes) {
      console.error('Unable to fetch document types', errorDocTypes);
      return [{ label: 'Unable to fetch document types', value: null }];
    }
    return (
      docTypes?.map((x) => ({
        value: x?.id,
        label: x?.description,
      })) || []
    );
  };

  const getCityOptions = () => {
    if (isLoadingCities) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }
    if (isErrorCities) {
      console.error('Unable to fetch cities', errorCities);
      return [{ label: 'Unable to fetch cities', value: null }];
    }
    return (
      cities?.map((x) => ({
        value: x?.id,
        label: x?.description,
      })) || []
    );
  };

  // Function to calculate currency totals from rows
  const calculateCurrencyTotals = (rowsData) => {
    const totals = {};

    // Iterate through all rows and sum amounts by currency
    Object.values(rowsData).forEach((row) => {
      if (row.currency && row.amount && !isNaN(parseFloat(row.amount))) {
        const currencyLabel = currencyOptions.find(c => c.value === row.currency)?.label || row.currency;
        const amount = parseFloat(row.amount);

        if (totals[currencyLabel]) {
          totals[currencyLabel] += amount;
        } else {
          totals[currencyLabel] = amount;
        }
      }
    });

    // Convert to array format for display
    return Object.entries(totals).map(([currency, total]) => ({
      currency,
      net_total: total.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    }));
  };

  // Update currency totals whenever rows change
  useEffect(() => {
    const newTotals = calculateCurrencyTotals(rows);
    setCurrencyTotals(newTotals);
  }, [rows, currencyOptions]);

  const handleAddRows = () => {
    const newRows = {};
    const id = crypto.randomUUID();
    // Find TMN currency option, fallback to 'DHS' if TMN not found
    const tmnCurrency = currencyOptions.find((x) => x.label === 'TMN');
    const defaultCurrency = tmnCurrency ? tmnCurrency.value : 'DHS';
    const defaultCurrencyId = tmnCurrency ? tmnCurrency.value : null;

    newRows[id] = {
      id,
      currency: defaultCurrency,
      currency_id: defaultCurrencyId, // Following JournalVoucherRow pattern
      rate: '', // Following JournalVoucherRow pattern
      amount: '',
      lc_amount: '', // Following JournalVoucherRow pattern
      narration: '',
      docType: '',
      docNo: '',
      bank: '',
      city: '',
      code: '',
      error: false, // Following JournalVoucherRow pattern
    };
    setRows((prevRows) => ({ ...prevRows, ...newRows }));
  };

  const handleSubmit = () => {
    const formValues = formikRef.current.values;
    console.log('formValues', formValues);
    console.log('selectedFiles', selectedFiles);
    let payload = {
      ...rows,
    };

    // Remove rows that have empty values
    payload = Object.fromEntries(
      Object.entries(payload).filter(([_, obj]) => {
        return Object.entries(obj).every(([_, v]) => {
          return v !== '' && v !== null && v !== undefined;
        });
      })
    );

    console.log('submit payload:', payload);
  };

  const handleCancel = () => {
    setRows(generateInitialRows(3, currencyOptions));
    setIsDisabled(true);
    // Reset selected accounts (copied from Account to Account)
    setSelectedDebitAccount(null);
    setSelectedCreditAccount(null);
    setNewAccountTriggeredFrom('');
    // Reset bank-related state (copied from Account to Account)
    setSelectedBank('');
    setChequeOptions([]);
    setShowChequeNumber(false);
    if (formikRef.current) {
      formikRef.current.resetForm();
    }
  };

  // Handler functions for rows
  const updateField = useCallback((id, field, value) => {
    setRows((prev) => {
      const newRows = {
        ...prev,
        [id]: {
          ...prev[id],
          [field]: value,
        },
      };

      return newRows;
    });
  }, []);

  const handleDeleteRow = (id) => {
    setRows((prevRows) => {
      const newState = { ...prevRows };
      delete newState[id];
      return newState;
    });
  };

  return (
    <>
      <div className="d-card">
        <Formik
          innerRef={formikRef}
          initialValues={{
            debitLedger: '',
            debitAccount: '',
            creditLedger: '',
            creditAccount: '',
            account_title: '',
          }}
          validationSchema={currencyTransferValidationSchema}
          onSubmit={handleSubmit}
        >
          {({ values, handleChange, handleBlur, setFieldValue }) => {
            // Helper function to handle account loading (copied from Account to Account)
            const handleLedgerChange = (
              ledgerType,
              fieldName,
              setAccountField
            ) => {
              setFieldValue(fieldName, ledgerType);
              setFieldValue(setAccountField, ''); // Clear account when ledger changes
            };

            return (
              <Form>
                <div className="row">
                  <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
                    <div className="row mb-4">
                      {/* Debit Account Section */}
                      <div className="col-12 col-sm-6 mb-45">
                        <CombinedInputs
                          label="Debit Account"
                          type1="select"
                          type2="select"
                          name1="debitLedger"
                          name2="debitAccount"
                          value1={values.debitLedger}
                          value2={
                            values.debitAccount ||
                            (newlyCreatedAccount?.id &&
                            newAccountTriggeredFrom === 'debit'
                              ? newlyCreatedAccount.id
                              : '')
                          }
                          options1={[
                            { label: 'PL', value: 'party' },
                            { label: 'GL', value: 'general' },
                            { label: 'WIC', value: 'walkin' },
                          ]}
                          options2={getAccountsByTypeOptions(
                            values.debitLedger
                          )}
                          isDisabled={isDisabled}
                          handleBlur={handleBlur}
                          placeholder1="Ledger"
                          placeholder2="Select Account"
                          className1="ledger"
                          className2="account"
                          onChange1={(selected) => {
                            handleLedgerChange(
                              selected.value,
                              'debitLedger',
                              'debitAccount'
                            );
                          }}
                          onChange2={(selected) => {
                            if (
                              selected.label
                                ?.toLowerCase()
                                ?.startsWith('add new')
                            ) {
                              setShowAddLedgerModal(
                                selected.label?.toLowerCase()
                              );
                              setNewAccountTriggeredFrom('debit');
                            } else {
                              setFieldValue('debitAccount', selected.value);
                              // Track selected debit account for balance fetching
                              setSelectedDebitAccount({
                                value: selected.value,
                                label: selected.label,
                                accountType: values.debitLedger,
                              });
                            }
                          }}
                        />
                      </div>

                      {/* Credit Account Section */}
                      <div className="col-12 col-sm-6 mb-45">
                        <div className="d-flex align-items-end gap-2">
                          <div className="flex-grow-1">
                            <CombinedInputs
                              label="Credit Account"
                              type1="select"
                              type2="select"
                              name1="creditLedger"
                              name2="creditAccount"
                              value1={values.creditLedger}
                              value2={
                                values.creditAccount ||
                                (newlyCreatedAccount?.id &&
                                newAccountTriggeredFrom === 'credit'
                                  ? newlyCreatedAccount.id
                                  : '')
                              }
                              options1={[
                                { label: 'PL', value: 'party' },
                                { label: 'GL', value: 'general' },
                                { label: 'WIC', value: 'walkin' },
                              ]}
                              options2={getAccountsByTypeOptions(
                                values.creditLedger
                              )}
                              isDisabled={isDisabled}
                              handleBlur={handleBlur}
                              placeholder1="Ledger"
                              placeholder2="Select Account"
                              className1="ledger"
                              className2="account"
                              onChange1={(selected) => {
                                handleLedgerChange(
                                  selected.value,
                                  'creditLedger',
                                  'creditAccount'
                                );
                              }}
                              onChange2={(selected) => {
                                if (
                                  selected.label
                                    ?.toLowerCase()
                                    ?.startsWith('add new')
                                ) {
                                  setShowAddLedgerModal(
                                    selected.label?.toLowerCase()
                                  );
                                  setNewAccountTriggeredFrom('credit');
                                } else {
                                  setFieldValue(
                                    'creditAccount',
                                    selected.value
                                  );
                                  // Track selected credit account for balance fetching
                                  setSelectedCreditAccount({
                                    value: selected.value,
                                    label: selected.label,
                                    accountType: values.creditLedger,
                                  });
                                }
                              }}
                            />
                          </div>
                        </div>
                      </div>
                      {/* Account Title */}
                      <div className="col-12 col-sm-6 mb-45">
                        <SearchableSelect
                          name="account_title"
                          label="Account Title"
                          options={[
                            { label: 'Show', value: 'show' },
                            { label: 'Hide', value: 'hide' },
                          ]}
                          value={values.account_title}
                          onChange={(selected) =>
                            setFieldValue('account_title', selected.value)
                          }
                          onBlur={handleBlur}
                          placeholder="Show"
                          isDisabled={isDisabled}
                        />
                      </div>
                    </div>
                  </div>
                  <div className="col-0  col-xxl-2" />
                  <div className="col-12 col-lg-10 col-xl-9 col-xxl-3">
                    <div className="row">
                      {/* Right side cards - Account Balance Cards (copied from Account to Account) */}
                      {showBalances && (
                        <>
                          {selectedDebitAccount && (
                            <div
                              className="col-12 mb-2"
                              style={{ maxWidth: '350px' }}
                            >
                              <AccountBalanceCard
                                heading="Debit Account Balance"
                                accountName={selectedDebitAccount.label}
                                balances={debitAccountBalance?.balances || []}
                                loading={debitAccountBalance === undefined}
                              />
                            </div>
                          )}
                          {selectedCreditAccount && (
                            <div
                              className="col-12 mb-2"
                              style={{ maxWidth: '350px' }}
                            >
                              <AccountBalanceCard
                                heading="Credit Account Balance"
                                accountName={selectedCreditAccount.label}
                                balances={creditAccountBalance?.balances || []}
                                loading={creditAccountBalance === undefined}
                              />
                            </div>
                          )}
                        </>
                      )}
                      <div
                        className="col-12 mb-4"
                        style={{ maxWidth: '350px' }}
                      >
                        <h6 className="mb-2">
                          Live Exchange Rates Against Base Currency
                        </h6>
                        <div className="d-card account-balance-card">
                          <div className="d-flex justify-content-between align-items-center mb-3">
                            <div className="d-flex align-items-center account-name w-100">
                              <span
                                className="me-2"
                                style={{ color: '#6B7280' }}
                              >
                                Inverse
                              </span>
                              <div className="form-check form-switch">
                                <input
                                  className="form-check-input"
                                  type="checkbox"
                                  style={{ cursor: 'pointer' }}
                                />
                              </div>
                            </div>
                          </div>
                          <table className="w-100">
                            <thead>
                              <tr style={{ borderBottom: '1px solid #E5E7EB' }}>
                                <th
                                  style={{
                                    padding: '8px 0',
                                    color: '#6B7280',
                                    fontWeight: '500',
                                  }}
                                >
                                  FCy
                                </th>
                                <th
                                  style={{
                                    padding: '8px 0',
                                    color: '#6B7280',
                                    fontWeight: '500',
                                  }}
                                >
                                  Rates
                                </th>
                                <th
                                  style={{
                                    padding: '8px 0',
                                    color: '#6B7280',
                                    fontWeight: '500',
                                  }}
                                >
                                  Change (24h)
                                </th>
                              </tr>
                            </thead>
                            <tbody>
                              {MOCK_EXCHANGE_RATES.map((rate, index) => (
                                <tr key={index}>
                                  <td style={{ padding: '8px 0' }}>
                                    {rate.currency}
                                  </td>
                                  <td style={{ padding: '8px 0' }}>
                                    {rate.rate}
                                  </td>
                                  <td
                                    style={{
                                      padding: '8px 0',
                                      color: rate.isPositive
                                        ? '#22C55E'
                                        : '#EF4444',
                                    }}
                                  >
                                    {rate.change}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>
                  <CustomTable
                    displayCard={false}
                    headers={currencyTransferNewHeaders}
                    isPaginated={false}
                    className={'inputTable'}
                    hideSearch
                    hideItemsPerPage
                  >
                    <tbody>
                      {Object.values(rows).map((row, index) => (
                        <CurrencyTransferRow
                          key={row.id}
                          row={row}
                          index={index}
                          isDisabled={isDisabled}
                          handleDeleteRow={handleDeleteRow}
                          updateField={updateField}
                          setShowMissingCurrencyRateModal={
                            setShowMissingCurrencyRateModal
                          }
                          setCurrencyToSelect={setCurrencyToSelect}
                          currencyOptions={currencyOptions} // Following JournalVoucherRow pattern
                          date={date} // Following JournalVoucherRow pattern
                          bankOptions={getBankOptions()} // Following General.js pattern
                          docTypeOptions={getDocTypeOptions()} // Following General.js pattern
                          cityOptions={getCityOptions()} // Following General.js pattern
                        />
                      ))}
                    </tbody>
                  </CustomTable>
                  <div className="my-3 d-flex justify-content-between flex-wrap">
                    <CustomButton
                      text="Add Special Commission"
                      variant="secondary"
                      disabled={isDisabled}
                      type="button"
                      className="w-auto px-5"
                      onClick={() =>
                        navigate('/transactions/special-comission')
                      }
                    />

                    <div className="d-card account-balance-card">
                      <table className="w-100">
                        <thead>
                          <tr style={{ borderBottom: '1px solid #E5E7EB' }}>
                            <th
                              style={{
                                padding: '8px 0',
                                color: '#6B7280',
                                fontWeight: '500',
                              }}
                            >
                              Currency
                            </th>
                            <th
                              style={{
                                padding: '8px 0',
                                color: '#6B7280',
                                fontWeight: '500',
                              }}
                            >
                              Net Total
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {currencyTotals.length > 0 ? (
                            currencyTotals.map((total, index) => (
                              <tr key={index}>
                                <td style={{ padding: '8px 0' }}>
                                  {total.currency}
                                </td>
                                <td style={{ padding: '8px 0' }}>
                                  {total.net_total}
                                </td>
                              </tr>
                            ))
                          ) : (
                            <tr>
                              <td colSpan="2" style={{ padding: '8px 0', textAlign: 'center', color: '#6B7280' }}>
                                No currencies selected
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </div>
                  </div>

                  <div className="d-flex flex-wrap justify-content-between mt-3 mb-5">
                    <div className="d-inline-block mt-3">
                      <CustomCheckbox
                        label="Account Balance"
                        disabled={isDisabled}
                        style={{ border: 'none', margin: 0 }}
                        onChange={(e) => setShowBalances(e.target.checked)}
                      />
                      <CustomCheckbox
                        label="Print"
                        disabled={isDisabled}
                        style={{ border: 'none', margin: 0 }}
                      />
                    </div>
                  </div>
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
      <VoucherNavigationBar
        isDisabled={isDisabled}
        actionButtons={[
          { text: 'Add Rows', onClick: handleAddRows },
          { text: 'Save', onClick: handleSubmit },
          { text: 'Cancel', onClick: handleCancel, variant: 'secondaryButton' },
        ]}
        onAttachmentClick={() => setUploadAttachmentsModal(true)}
        lastVoucherHeading="Last FSN Number"
        lastVoucherNumber={23}
      />
      {/* Upload Attachements Modal */}
      <CustomModal
        show={uploadAttachmentsModal}
        close={() => setUploadAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          uploadOnly
          getUploadedFiles={setSelectedFiles}
          closeUploader={() => setUploadAttachmentsModal(false)}
        />
      </CustomModal>
    </>
  );
};

export default NewCurrencyTransfer;
