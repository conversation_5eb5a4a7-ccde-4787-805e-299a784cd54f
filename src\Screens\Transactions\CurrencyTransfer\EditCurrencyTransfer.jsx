import { Form, Formik } from 'formik';
import React, { useCallback, useRef, useState } from 'react';
import AccountBalanceCard from '../../../Components/AccountBalanceCard/AccountBalanceCard';
import AttachmentsView from '../../../Components/AttachmentsView/AttachmentsView';
import CustomCheckbox from '../../../Components/CustomCheckbox/CustomCheckbox';
import CustomInput from '../../../Components/CustomInput';
import CustomModal from '../../../Components/CustomModal';
import CustomTable from '../../../Components/CustomTable/CustomTable';
import SearchableSelect from '../../../Components/SearchableSelect/SearchableSelect';
import VoucherNavigationBar from '../../../Components/VoucherNavigationBar/VoucherNavigationBar';
import {
  MOCK_CURRENCY_TRANSFER_VIEW_DATA,
  supportLogsData,
} from '../../../Mocks/MockData';
import { currencyTransferNewHeaders } from '../../../Utils/Constants/TableHeaders';
import CurrencyTransferRow from './CurrencyTransferRow';
import { currencyTransferValidationSchema } from '../../../Utils/Validations/ValidationSchemas';
import { useQuery } from '@tanstack/react-query';
import { getBanks as getGeneralBanks, getDocTypes, getCities } from '../../../Services/General';

const generateInitialRows = (count, currencyOptions = []) => {
  const rows = {};
  // Find TMN currency option for default currency
  const tmnCurrency = currencyOptions.find((x) => x.label === 'TMN');

  MOCK_CURRENCY_TRANSFER_VIEW_DATA?.tableData.forEach((row) => {
    const id = crypto.randomUUID();
    rows[id] = {
      id,
      currency: row.currency,
      currency_id: null, // Following JournalVoucherRow pattern
      rate: row.rate || '', // Following JournalVoucherRow pattern
      amount: row.amount,
      lc_amount: row.lc_amount || '', // Following JournalVoucherRow pattern
      narration: row.narration,
      docType: row.docType,
      docNo: row.docNo,
      bank: row.bank,
      city: row.city,
      code: row.code || '',
      error: false, // Following JournalVoucherRow pattern
    };
  });
  return rows;
};
// INITIAL_STATE will be set dynamically when currencyOptions are available

const EditCurrencyTransfer = ({
  setPageState,
  setShowAddOfficeLocationModal,
  setShowAddLedgerModal,
  newlyCreatedAccount,
  uploadAttachmentsModal,
  setUploadAttachmentsModal,
  selectedFiles,
  setSelectedFiles,
  setShowMissingCurrencyRateModal,
  setCurrencyToSelect,
  currencyOptions = [], // Following JournalVoucherRow pattern
  date, // Following JournalVoucherRow pattern
}) => {
  const [rows, setRows] = useState({});
  const [totalDebit, setTotalDebit] = useState(0);
  const [totalCredit, setTotalCredit] = useState(0);
  const [currencyTotals, setCurrencyTotals] = useState([]); // Track totals by currency
  const formikRef = useRef();

  // Fetch Banks (following General.js pattern)
  const {
    data: banks,
    isLoading: isLoadingBanks,
    isError: isErrorBanks,
    error: errorBanks,
  } = useQuery({
    queryKey: ['banks'],
    queryFn: getGeneralBanks,
    staleTime: 1000 * 60 * 5, // 5 minutes cache
  });

  // Fetch Document Types (following General.js pattern)
  const {
    data: docTypes,
    isLoading: isLoadingDocTypes,
    isError: isErrorDocTypes,
    error: errorDocTypes,
  } = useQuery({
    queryKey: ['doc-types'],
    queryFn: getDocTypes,
    staleTime: 1000 * 60 * 5, // 5 minutes cache
  });

  // Fetch Cities (following General.js pattern)
  const {
    data: cities,
    isLoading: isLoadingCities,
    isError: isErrorCities,
    error: errorCities,
  } = useQuery({
    queryKey: ['cities'],
    queryFn: getCities,
    staleTime: 1000 * 60 * 5, // 5 minutes cache
  });

  // Initialize rows with existing data when currencyOptions are available
  useEffect(() => {
    if (currencyOptions.length > 0 && Object.keys(rows).length === 0) {
      setRows(generateInitialRows(3, currencyOptions));
    }
  }, [currencyOptions, rows]);

  // Helper functions to transform data into options (following General.js pattern)
  const getBankOptions = () => {
    if (isLoadingBanks) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }
    if (isErrorBanks) {
      console.error('Unable to fetch banks', errorBanks);
      return [{ label: 'Unable to fetch banks', value: null }];
    }
    return banks?.map((x) => ({
      value: x?.id,
      label: x?.account_name,
    })) || [];
  };

  const getDocTypeOptions = () => {
    if (isLoadingDocTypes) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }
    if (isErrorDocTypes) {
      console.error('Unable to fetch document types', errorDocTypes);
      return [{ label: 'Unable to fetch document types', value: null }];
    }
    return docTypes?.map((x) => ({
      value: x?.id,
      label: x?.description,
    })) || [];
  };

  const getCityOptions = () => {
    if (isLoadingCities) {
      return [{ label: 'Loading...', value: null, isDisabled: true }];
    }
    if (isErrorCities) {
      console.error('Unable to fetch cities', errorCities);
      return [{ label: 'Unable to fetch cities', value: null }];
    }
    return cities?.map((x) => ({
      value: x?.id,
      label: x?.description,
    })) || [];
  };

  // Function to calculate currency totals from rows
  const calculateCurrencyTotals = (rowsData) => {
    const totals = {};

    // Iterate through all rows and sum amounts by currency
    Object.values(rowsData).forEach((row) => {
      if (row.currency && row.amount && !isNaN(parseFloat(row.amount))) {
        const currencyLabel = currencyOptions.find(c => c.value === row.currency)?.label || row.currency;
        const amount = parseFloat(row.amount);

        if (totals[currencyLabel]) {
          totals[currencyLabel] += amount;
        } else {
          totals[currencyLabel] = amount;
        }
      }
    });

    // Convert to array format for display
    return Object.entries(totals).map(([currency, total]) => ({
      currency,
      net_total: total.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    }));
  };

  // Update currency totals whenever rows change
  useEffect(() => {
    const newTotals = calculateCurrencyTotals(rows);
    setCurrencyTotals(newTotals);
  }, [rows, currencyOptions]);

  const handleAddRows = () => {
    const newRows = {};
    const id = crypto.randomUUID();
    // Find TMN currency option, fallback to 'DHS' if TMN not found
    const tmnCurrency = currencyOptions.find((x) => x.label === 'TMN');
    const defaultCurrency = tmnCurrency ? tmnCurrency.value : 'DHS';
    const defaultCurrencyId = tmnCurrency ? tmnCurrency.value : null;

    newRows[id] = {
      id,
      currency: defaultCurrency,
      currency_id: defaultCurrencyId, // Following JournalVoucherRow pattern
      rate: '', // Following JournalVoucherRow pattern
      amount: '',
      lc_amount: '', // Following JournalVoucherRow pattern
      narration: '',
      docType: '',
      docNo: '',
      bank: '',
      city: '',
      code: '',
      error: false, // Following JournalVoucherRow pattern
    };
    setRows((prevRows) => ({ ...prevRows, ...newRows }));
  };

  const handleSubmit = () => {
    const formValues = formikRef.current.values;
    console.log('formValues', formValues);
    console.log('selectedFiles', selectedFiles);
    let payload = {
      ...rows,
    };

    // Remove rows that have empty values
    payload = Object.fromEntries(
      Object.entries(payload).filter(([_, obj]) => {
        return Object.entries(obj).every(([_, v]) => {
          return v !== '' && v !== null && v !== undefined;
        });
      })
    );

    console.log('submit payload:', payload);
  };

  const handleCancel = () => {
    setPageState('new');
  };

  // Handler functions for rows
  const updateField = useCallback((id, field, value) => {
    setRows((prev) => {
      const newRows = {
        ...prev,
        [id]: {
          ...prev[id],
          [field]: value,
        },
      };

      return newRows;
    });
  }, []);

  const handleDeleteRow = (id) => {
    setRows((prevRows) => {
      const newState = { ...prevRows };
      delete newState[id];
      return newState;
    });
  };

  return (
    <>
      <div className="d-card">
        <Formik
          innerRef={formikRef}
          initialValues={{
            debitAccountParty: MOCK_CURRENCY_TRANSFER_VIEW_DATA.debitAccount.party,
            debitAccount: MOCK_CURRENCY_TRANSFER_VIEW_DATA.debitAccount.value,
            creditAccountParty: MOCK_CURRENCY_TRANSFER_VIEW_DATA.creditAccount.party,
            creditAccount: MOCK_CURRENCY_TRANSFER_VIEW_DATA.creditAccount.value,
            accountTitle: MOCK_CURRENCY_TRANSFER_VIEW_DATA.accountTitle.value,
          }}
          validationSchema={currencyTransferValidationSchema}
          onSubmit={handleSubmit}
        >
          {({ values, handleChange, handleBlur, setFieldValue }) => (
            <Form>
              <div className="row">
                <div className="col-12 col-lg-10 col-xl-9 col-xxl-7">
                  <div className="row mb-4">
                    {/* Debit Account Section */}
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name={'debitAccountParty'}
                        label={'Debit Account Party'}
                        options={[
                          { label: 'Party', value: 'Party' }
                        ]}
                        placeholder={'Select Party'}
                        value={values.debitAccountParty}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name={'debitAccount'}
                        label={'Debit Account'}
                        options={[
                          { label: 'Account ABC', value: 'a1' }
                        ]}
                        placeholder={'Select Account'}
                        value={values.debitAccount}
                        onChange={(selected) => setFieldValue('debitAccount', selected.value)}
                        onBlur={handleBlur}
                      />
                    </div>

                    {/* Credit Account Section */}
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name={'creditAccountParty'}
                        label={'Credit Account Party'}
                        options={[
                          { label: 'Party', value: 'Party' }
                        ]}
                        placeholder={'Select Party'}
                        value={values.creditAccountParty}
                        onChange={handleChange}
                        onBlur={handleBlur}
                      />
                    </div>
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name={'creditAccount'}
                        label={'Credit Account'}
                        options={[
                          { label: 'Account ABC', value: 'a1' }
                        ]}
                        placeholder={'Select Account'}
                        value={values.creditAccount}
                        onChange={(selected) => setFieldValue('creditAccount', selected.value)}
                        onBlur={handleBlur}
                      />
                    </div>

                    {/* Account Title */}
                    <div className="col-12 col-sm-6 mb-45">
                      <SearchableSelect
                        name={'accountTitle'}
                        label={'Account Title'}
                        options={[
                          { label: 'Show', value: 'show' }
                        ]}
                        placeholder={'Select Title'}
                        value={values.accountTitle}
                        onChange={(selected) => setFieldValue('accountTitle', selected.value)}
                        onBlur={handleBlur}
                      />
                    </div>
                  </div>
                </div>
                <div className="col-0  col-xxl-2" />
                <div className="col-12 col-lg-10 col-xl-9 col-xxl-3">
                  <div className="row">
                    {/* Right side cards */}
                    <div className="col-12 mb-5" style={{ maxWidth: '350px' }}>
                      <AccountBalanceCard />
                    </div>
                  </div>
                </div>
                <CustomTable
                  displayCard={false}
                  headers={currencyTransferNewHeaders}
                  isPaginated={false}
                  className={'inputTable'}
                  hideSearch
                  hideItemsPerPage
                >
                  <tbody>
                    {Object.values(rows).map((row, index) => (
                      <CurrencyTransferRow
                        key={row.id}
                        row={row}
                        index={index}
                        handleDeleteRow={handleDeleteRow}
                        updateField={updateField}
                        setShowMissingCurrencyRateModal={setShowMissingCurrencyRateModal}
                        setCurrencyToSelect={setCurrencyToSelect}
                        currencyOptions={currencyOptions} // Following JournalVoucherRow pattern
                        date={date} // Following JournalVoucherRow pattern
                        bankOptions={getBankOptions()} // Following General.js pattern
                        docTypeOptions={getDocTypeOptions()} // Following General.js pattern
                        cityOptions={getCityOptions()} // Following General.js pattern
                      />
                    ))}
                  </tbody>
                </CustomTable>

                <div className="d-flex flex-wrap justify-content-between mt-3 mb-5">
                  <div className="d-inline-block mt-3">
                    <CustomCheckbox
                      label="Account Balance"
                      style={{ border: 'none', margin: 0 }}
                      onChange={() => {}}
                    />
                    <CustomCheckbox
                      label="Print"
                      style={{ border: 'none', margin: 0 }}
                    />
                  </div>
                  <div className="d-flex flex-column gap-2 mt-1 debit-credit-inputs">
                    <CustomInput
                      name="totalDebit"
                      label={'Total Debit'}
                      labelClass={'fw-medium'}
                      type="number"
                      error={false}
                      borderRadius={10}
                      value={totalDebit.toFixed(2)}
                      readOnly
                    />
                    <CustomInput
                      name="totalCredit"
                      label={'Total Credit'}
                      labelClass={'fw-medium'}
                      type="number"
                      error={false}
                      borderRadius={10}
                      value={totalCredit.toFixed(2)}
                      readOnly
                    />
                  </div>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
      <VoucherNavigationBar
        actionButtons={[
          { text: 'Add Rows', onClick: handleAddRows },
          { text: 'Save', onClick: handleSubmit },
          { text: 'Cancel', onClick: handleCancel, variant: 'secondaryButton' },
        ]}
        onAttachmentClick={() => setUploadAttachmentsModal(true)}
        lastVoucherHeading="Last TRQ Number"
        lastVoucherNumber={23}
      />
      {/* Upload Attachements Modal */}
      <CustomModal
        show={uploadAttachmentsModal}
        close={() => setUploadAttachmentsModal(false)}
        background={true}
      >
        <AttachmentsView
          item={supportLogsData[0]}
          closeUploader={() => setUploadAttachmentsModal(false)}
        />
      </CustomModal>
    </>
  );
};

export default EditCurrencyTransfer;
